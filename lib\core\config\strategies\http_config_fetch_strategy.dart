/// HTTP配置获取策略
///
/// 通过HTTP请求从远程服务器获取配置信息
library;

import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import '../config_manager_interface.dart';
import '../../network/network_service.dart';
import '../../app/app_config.dart';

/// HTTP配置获取策略
class HttpConfigFetchStrategy implements IConfigFetchStrategy {
  /// 网络服务实例
  late final NetworkService _networkService;
  
  /// 配置接口URL
  final String configUrl;
  
  /// 请求头
  final Map<String, String> headers;
  
  /// 请求超时时间
  final Duration timeout;
  
  /// 重试次数
  final int retryCount;
  
  /// 重试间隔
  final Duration retryDelay;

  HttpConfigFetchStrategy({
    required this.configUrl,
    this.headers = const {},
    this.timeout = const Duration(seconds: 30),
    this.retryCount = 3,
    this.retryDelay = const Duration(seconds: 2),
  });

  @override
  String get name => 'http';

  @override
  int get priority => 1; // 最高优先级

  @override
  bool get isAvailable {
    try {
      return Get.isRegistered<NetworkService>() && configUrl.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<Map<String, dynamic>> fetchConfigs() async {
    if (!isAvailable) {
      throw Exception('HTTP配置获取策略不可用：网络服务未初始化或配置URL为空');
    }

    _networkService = Get.find<NetworkService>();
    
    Exception? lastException;
    
    // 重试机制
    for (int attempt = 1; attempt <= retryCount; attempt++) {
      try {
        debugPrint('开始第 $attempt 次尝试获取远程配置...');
        
        final response = await _networkService.get(
          configUrl,
          options: _buildRequestOptions(),
        );

        if (response.statusCode == 200) {
          final data = response.data;
          
          if (data is Map<String, dynamic>) {
            debugPrint('成功获取远程配置，配置项数量: ${data.length}');
            return _processConfigData(data);
          } else if (data is String) {
            // 尝试解析JSON字符串
            final jsonData = json.decode(data) as Map<String, dynamic>;
            debugPrint('成功解析远程配置JSON，配置项数量: ${jsonData.length}');
            return _processConfigData(jsonData);
          } else {
            throw Exception('远程配置数据格式不正确：期望Map<String, dynamic>，实际: ${data.runtimeType}');
          }
        } else {
          throw Exception('远程配置请求失败：HTTP ${response.statusCode}');
        }
      } catch (e) {
        lastException = e is Exception ? e : Exception(e.toString());
        debugPrint('第 $attempt 次获取远程配置失败: $e');
        
        if (attempt < retryCount) {
          debugPrint('等待 ${retryDelay.inSeconds} 秒后重试...');
          await Future.delayed(retryDelay);
        }
      }
    }

    throw lastException ?? Exception('获取远程配置失败：未知错误');
  }

  /// 构建请求选项
  Map<String, dynamic> _buildRequestOptions() {
    final options = <String, dynamic>{
      'timeout': timeout.inMilliseconds,
      'headers': {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        ...headers,
      },
    };

    // 添加应用信息到请求头
    try {
      final appConfig = AppConfig.instance;
      options['headers'].addAll({
        'X-App-Version': appConfig.version,
        'X-App-Build': appConfig.buildNumber,
        'X-Platform': appConfig.platform,
      });
    } catch (e) {
      debugPrint('添加应用信息到请求头失败: $e');
    }

    return options;
  }

  /// 处理配置数据
  Map<String, dynamic> _processConfigData(Map<String, dynamic> rawData) {
    final processedData = <String, dynamic>{};
    
    // 扁平化嵌套的配置数据
    _flattenConfigData(rawData, processedData);
    
    // 验证配置数据
    _validateConfigData(processedData);
    
    return processedData;
  }

  /// 扁平化配置数据
  void _flattenConfigData(
    Map<String, dynamic> source,
    Map<String, dynamic> target, [
    String prefix = '',
  ]) {
    source.forEach((key, value) {
      final fullKey = prefix.isEmpty ? key : '$prefix.$key';
      
      if (value is Map<String, dynamic>) {
        // 递归处理嵌套对象
        _flattenConfigData(value, target, fullKey);
      } else {
        // 直接添加到目标Map
        target[fullKey] = value;
      }
    });
  }

  /// 验证配置数据
  void _validateConfigData(Map<String, dynamic> data) {
    if (data.isEmpty) {
      throw Exception('远程配置数据为空');
    }

    // 检查是否包含必要的配置项
    final requiredKeys = ['app.version_check_url', 'network.base_url'];
    for (final key in requiredKeys) {
      if (!data.containsKey(key)) {
        debugPrint('警告：缺少必要的配置项: $key');
      }
    }

    debugPrint('配置数据验证通过，包含 ${data.length} 个配置项');
  }
}
